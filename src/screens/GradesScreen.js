import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ScrollView,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faArrowLeft, faChartLine } from '@fortawesome/free-solid-svg-icons';
import { useScreenOrientation } from '../hooks/useScreenOrientation';

export default function GradesScreen({ navigation, route }) {
  const [activeTab, setActiveTab] = useState('summative');
  const [screenData, setScreenData] = useState(Dimensions.get('window'));
  const { studentName, authCode } = route.params || {};
  const [grades, setGrades] = useState(null);
  const [loading, setLoading] = useState(false);

  // Enable rotation for this screen
  useScreenOrientation(true);

  // Listen for orientation changes
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenData(window);
    });

    return () => subscription?.remove();
  }, []);

  // Determine if device is in landscape mode
  const isLandscape = screenData.width > screenData.height;

  // Fetch grades data
  const fetchGrades = async () => {
    if (!authCode) {
      console.log('No authCode provided');
      return;
    }

    try {
      setLoading(true);
      console.log('Fetching grades with authCode:', authCode);
      const url = `https://sis.bfi.edu.mm/mobile-api/get-student-grades?authCode=${authCode}`;
      console.log('Request URL:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });

      console.log('Response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('Raw grades data:', data);
        setGrades(data);
      } else {
        console.error(
          'Failed to fetch grades:',
          response.status,
          response.statusText
        );
        const errorText = await response.text();
        console.error('Error response body:', errorText);
      }
    } catch (error) {
      console.error('Error fetching grades:', error);
      console.error('Error details:', {
        message: error.message,
        name: error.name,
        stack: error.stack,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchGrades();
  }, [authCode]);

  const renderTabButton = (tabName, title) => (
    <TouchableOpacity
      key={tabName}
      style={[
        styles.tabButton,
        activeTab === tabName && styles.activeTabButton,
      ]}
      onPress={() => setActiveTab(tabName)}
    >
      <Text
        style={[
          styles.tabButtonText,
          activeTab === tabName && styles.activeTabButtonText,
        ]}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );

  const renderSummativeTableHeader = () => {
    if (isLandscape) {
      // Show all columns in landscape mode
      return (
        <View style={styles.tableHeader}>
          <Text style={[styles.headerCell, styles.dateColumn]}>Date</Text>
          <Text style={[styles.headerCell, styles.subjectColumn]}>Subject</Text>
          <Text style={[styles.headerCell, styles.strandColumn]}>Strand</Text>
          <Text style={[styles.headerCell, styles.titleColumn]}>Title</Text>
          <Text style={[styles.headerCell, styles.scoreColumn]}>Score</Text>
          <Text style={[styles.headerCell, styles.percentageColumn]}>%</Text>
          <Text style={[styles.headerCell, styles.typeColumn]}>Type</Text>
          <Text style={[styles.headerCell, styles.teacherColumn]}>Teacher</Text>
        </View>
      );
    } else {
      // Show only important columns in portrait mode
      return (
        <View style={styles.tableHeader}>
          <Text style={[styles.headerCell, styles.portraitDateColumn]}>
            Date
          </Text>
          <Text style={[styles.headerCell, styles.portraitSubjectColumn]}>
            Subject
          </Text>
          <Text style={[styles.headerCell, styles.portraitTitleColumn]}>
            Title
          </Text>
          <Text style={[styles.headerCell, styles.portraitScoreColumn]}>
            Score
          </Text>
          <Text style={[styles.headerCell, styles.portraitPercentageColumn]}>
            %
          </Text>
        </View>
      );
    }
  };

  const renderSummativeRow = ({ item, index }) => {
    if (isLandscape) {
      // Show all columns in landscape mode
      return (
        <View style={[styles.tableRow, index % 2 === 0 && styles.evenRow]}>
          <Text style={[styles.cell, styles.dateColumn]}>
            {item.date || 'N/A'}
          </Text>
          <Text style={[styles.cell, styles.subjectColumn]}>
            {item.subject || 'N/A'}
          </Text>
          <Text style={[styles.cell, styles.strandColumn]}>
            {item.strand || 'N/A'}
          </Text>
          <Text style={[styles.cell, styles.titleColumn]}>
            {item.title || 'N/A'}
          </Text>
          <Text style={[styles.cell, styles.scoreColumn]}>
            {item.score || 'N/A'}
          </Text>
          <Text style={[styles.cell, styles.percentageColumn]}>
            {item.percentage || 'N/A'}
          </Text>
          <Text style={[styles.cell, styles.typeColumn]}>
            {item.type || 'N/A'}
          </Text>
          <Text style={[styles.cell, styles.teacherColumn]}>
            {item.teacher || 'N/A'}
          </Text>
        </View>
      );
    } else {
      // Show only important columns in portrait mode
      return (
        <View style={[styles.tableRow, index % 2 === 0 && styles.evenRow]}>
          <Text style={[styles.cell, styles.portraitDateColumn]}>
            {item.date || 'N/A'}
          </Text>
          <Text style={[styles.cell, styles.portraitSubjectColumn]}>
            {item.subject || 'N/A'}
          </Text>
          <Text style={[styles.cell, styles.portraitTitleColumn]}>
            {item.title || 'N/A'}
          </Text>
          <Text style={[styles.cell, styles.portraitScoreColumn]}>
            {item.score || 'N/A'}
          </Text>
          <Text style={[styles.cell, styles.portraitPercentageColumn]}>
            {item.percentage || 'N/A'}
          </Text>
        </View>
      );
    }
  };

  const renderSummativeContent = () => {
    // Use real API data if available, otherwise show dummy data
    let summativeData = [];

    if (grades?.summative && Array.isArray(grades.summative)) {
      // Transform API data to match our table format
      summativeData = grades.summative.map((item, index) => ({
        id: index + 1,
        date: item.date || 'N/A',
        subject: item.subject_name || 'N/A',
        strand: 'N/A', // Not provided in API response
        title: item.assessment_name || 'N/A',
        score: item.score ? `${item.score}/100` : 'N/A',
        percentage: item.score_percentage ? `${item.score_percentage}%` : 'N/A',
        type: item.type_title || 'N/A',
        teacher: item.teacher_name || 'N/A',
      }));
    } else {
      // Fallback dummy data for testing
      summativeData = [
        {
          id: 1,
          date: '2025-03-19',
          subject: 'Mathematics',
          strand: 'Geometry',
          title: 'Geometry Exam',
          score: '93/100',
          percentage: '93%',
          type: 'Major',
          teacher: 'Su Su Htwe',
        },
        {
          id: 2,
          date: '2024-01-20',
          subject: 'English',
          strand: 'Literature',
          title: 'Essay Writing',
          score: '92/100',
          percentage: '92%',
          type: 'Assignment',
          teacher: 'Ms. Johnson',
        },
        {
          id: 3,
          date: '2024-01-25',
          subject: 'Physics',
          strand: 'Mechanics',
          title: 'Motion & Forces',
          score: '78/100',
          percentage: '78%',
          type: 'Quiz',
          teacher: 'Dr. Brown',
        },
      ];
    }

    return (
      <View
        style={[
          styles.tableContainer,
          isLandscape && styles.landscapeTableContainer,
        ]}
      >
        {renderSummativeTableHeader()}
        <FlatList
          data={summativeData}
          renderItem={renderSummativeRow}
          keyExtractor={(item) => item.id.toString()}
          showsVerticalScrollIndicator={false}
          style={styles.tableBody}
        />
      </View>
    );
  };

  const renderFormativeContent = () => {
    // Use real API data if available, otherwise show message
    let formativeData = [];

    if (grades?.formative && Array.isArray(grades.formative)) {
      // Transform API data to match our table format
      formativeData = grades.formative.map((item, index) => ({
        id: index + 1,
        date: item.date || 'N/A',
        subject: item.subject_name || 'N/A',
        strand: 'N/A', // Not provided in API response
        title: item.assessment_name || 'N/A',
        score: item.score ? `${item.score}/100` : 'N/A',
        percentage: item.score_percentage ? `${item.score_percentage}%` : 'N/A',
        type: item.type_title || 'N/A',
        teacher: item.teacher_name || 'N/A',
      }));
    }

    if (formativeData.length === 0) {
      return (
        <View style={styles.comingSoon}>
          <Text style={styles.comingSoonText}>
            {loading
              ? 'Loading formative grades...'
              : 'No formative grades available'}
          </Text>
        </View>
      );
    }

    return (
      <View
        style={[
          styles.tableContainer,
          isLandscape && styles.landscapeTableContainer,
        ]}
      >
        {renderSummativeTableHeader()}
        <FlatList
          data={formativeData}
          renderItem={renderSummativeRow}
          keyExtractor={(item) => item.id.toString()}
          showsVerticalScrollIndicator={false}
          style={styles.tableBody}
        />
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <FontAwesomeIcon icon={faArrowLeft} size={20} color='#fff' />
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <FontAwesomeIcon icon={faChartLine} size={20} color='#fff' />
          <Text style={styles.headerTitle}>Grades</Text>
        </View>
        <View style={styles.headerRight} />
      </View>

      {studentName && (
        <View style={styles.studentInfo}>
          <Text style={styles.studentNameText}>Grades for {studentName}</Text>
        </View>
      )}

      <View style={styles.content}>
        {/* Tab Buttons */}
        <View style={styles.tabContainer}>
          {renderTabButton('summative', 'Summative')}
          {renderTabButton('formative', 'Formative')}
        </View>

        {/* Tab Content */}
        {isLandscape ? (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.scrollContainer}
          >
            {activeTab === 'summative'
              ? renderSummativeContent()
              : renderFormativeContent()}
          </ScrollView>
        ) : (
          <View style={styles.scrollContainer}>
            {activeTab === 'summative'
              ? renderSummativeContent()
              : renderFormativeContent()}
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#FF9500',
    padding: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  headerRight: {
    width: 36,
  },
  studentInfo: {
    backgroundColor: '#fff',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  studentNameText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 15,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 25,
    padding: 4,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 20,
    alignItems: 'center',
  },
  activeTabButton: {
    backgroundColor: '#FF9500',
  },
  tabButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  activeTabButtonText: {
    color: '#fff',
  },
  scrollContainer: {
    flex: 1,
  },
  tableContainer: {
    backgroundColor: '#fff',
    borderRadius: 10,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  landscapeTableContainer: {
    minWidth: Dimensions.get('window').width * 2, // Make table scrollable horizontally in landscape
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#FF9500',
    paddingVertical: 12,
    paddingHorizontal: 8,
  },
  headerCell: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
    paddingHorizontal: 4,
  },
  tableBody: {
    maxHeight: 600,
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  evenRow: {
    backgroundColor: '#f9f9f9',
  },
  cell: {
    fontSize: 11,
    color: '#333',
    textAlign: 'center',
    paddingHorizontal: 4,
  },
  // Column widths
  dateColumn: {
    width: 80,
  },
  subjectColumn: {
    width: 100,
  },
  strandColumn: {
    width: 90,
  },
  titleColumn: {
    width: 120,
  },
  scoreColumn: {
    width: 60,
  },
  percentageColumn: {
    width: 50,
  },
  typeColumn: {
    width: 80,
  },
  teacherColumn: {
    width: 100,
  },
  comingSoon: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 40,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  comingSoonText: {
    fontSize: 16,
    color: '#666',
    fontStyle: 'italic',
  },
  // Portrait mode column widths (important columns only)
  portraitDateColumn: {
    width: 70,
  },
  portraitSubjectColumn: {
    width: 80,
  },
  portraitTitleColumn: {
    width: 100,
  },
  portraitScoreColumn: {
    width: 60,
  },
  portraitPercentageColumn: {
    width: 50,
  },
});
