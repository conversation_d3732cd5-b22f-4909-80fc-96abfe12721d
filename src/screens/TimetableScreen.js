import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import {
  faArrowLeft,
  faCalendarAlt,
  faClock,
} from '@fortawesome/free-solid-svg-icons';
import timetableData from '../data/dummyTimetable.json';



const screenWidth = Dimensions.get('window').width;

export default function TimetableScreen({ navigation, route }) {
  const [timetable, setTimetable] = useState(null);
  const { studentName, authCode } = route.params || {};
  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

  const fetchTimetable = async () => {
    try {
      const response = await fetch(
        `https://sis.bfi.edu.mm/mobile-api/get-student-timetable2?authCode=${authCode}`
      );
      if (response.ok) {
        const data = await response.json();
        return data;
      } else {
        console.error('Failed to fetch timetable:', response.statusText);
        return null;
      }
    } catch (error) {
      console.error('Error fetching timetable:', error);
      return null;
    }
  };
  useEffect(() => {
    const fetchAndSetTimetable = async () => {
      const data = await fetchTimetable();
      if (data) {
        console.log(data);
        setTimetable(data);
      }
    };

    fetchAndSetTimetable();
  }, []);

  const getCurrentDay = () => {
    const today = new Date().getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    return days[today - 1] || 'Monday'; // Adjust for Monday-Friday schedule
  };

  const [selectedDay, setSelectedDay] = useState(getCurrentDay());

  const getSubjectColor = (subject) => {
    const colors = {
      Math: '#FF6B6B',
      English: '#4ECDC4',
      Physics: '#45B7D1',
      Chemistry: '#96CEB4',
      Biology: '#FFEAA7',
      History: '#DDA0DD',
      Geography: '#98D8C8',
      Art: '#F7DC6F',
      Music: '#BB8FCE',
      PE: '#85C1E9',
      'Physical Education': '#85C1E9',
      'Computer Science': '#82E0AA',
      'Computer Lab': '#82E0AA',
      Health: '#F8C471',
      Drama: '#D7BDE2',
    };
    return colors[subject] || '#BDC3C7';
  };

  const renderTimeSlot = ({ item, index }) => (
    <View style={styles.timeSlotContainer}>
      <View style={styles.periodContainer}>
        <Text style={styles.periodText}>{index + 1}</Text>
      </View>
      <View
        style={[
          styles.subjectContainer,
          { backgroundColor: getSubjectColor(item.subject) },
        ]}
      >
        <Text style={styles.subjectText}>{item.subject}</Text>
        <Text style={styles.teacherText}>Teacher name</Text>
      </View>
      
    </View>
  );

  const renderDayTab = (day) => (
    <TouchableOpacity
      key={day}
      style={[styles.dayTab, selectedDay === day && styles.selectedDayTab]}
      onPress={() => setSelectedDay(day)}
    >
      <Text
        style={[
          styles.dayTabText,
          selectedDay === day && styles.selectedDayTabText,
        ]}
      >
        {day.substring(0, 3)}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <FontAwesomeIcon icon={faArrowLeft} size={20} color='#fff' />
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <FontAwesomeIcon icon={faCalendarAlt} size={20} color='#fff' />
          <Text style={styles.headerTitle}>Timetable</Text>
        </View>
        <View style={styles.headerRight} />
      </View>

      {studentName && (
        <View style={styles.studentInfo}>
          <Text style={styles.studentNameText}>Schedule for {studentName}</Text>
        </View>
      )}

      <View style={styles.content}>
        {/* Day Tabs */}
        <View
          style={styles.dayTabsContainer}
          contentContainerStyle={styles.dayTabsContent}
        >
          {days.map(renderDayTab)}
        </View>

        {/* Selected Day Schedule */}
        <View style={styles.scheduleContainer}>
          <Text style={styles.dayTitle}>{selectedDay}</Text>
          <FlatList
            data={timetableData[selectedDay]}
            renderItem={renderTimeSlot}
            keyExtractor={(item, index) => `${selectedDay}-${index}`}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scheduleList}
          />
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#AF52DE',
    padding: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  headerRight: {
    width: 36,
  },
  studentInfo: {
    backgroundColor: '#fff',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  studentNameText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 15,
  },
  dayTabsContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
    maxHeight: 200,
  },
  dayTabsContent: {
    paddingHorizontal: 5,
    width: screenWidth - 10,
    justifyContent: 'space-evenly',
  },
  dayTab: {
    flex: 1,
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 8, // Reduced from 12
    borderRadius: 20, // Reduced from 25
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    width: '100%',
    //justifyContent: 'center',
    alignItems: 'center',
  },
  selectedDayTab: {
    backgroundColor: '#AF52DE',
  },
  dayTabText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666',
  },
  selectedDayTabText: {
    color: '#fff',
  },
  scheduleContainer: {
    flex: 1,
  },
  dayTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    textAlign: 'center',
  },
  scheduleList: {
    paddingBottom: 10,
  },
  timeSlotContainer: {
    flexDirection: 'row',
    marginBottom: 10,
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  periodContainer: {
    marginRight: 10,
    justifyContent: 'center',
  },
  periodText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  teacherContainer: {
    marginLeft: 'auto',
    justifyContent: 'center',
  },
  teacherText: {
    fontSize: 12,
    color: '#333',
    te
  },
  subjectContainer: {
    flex: 1,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    justifyContent: 'center',
  },
  subjectText: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
  },
});
